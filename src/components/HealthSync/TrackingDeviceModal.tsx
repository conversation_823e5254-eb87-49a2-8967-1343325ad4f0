import {useState} from 'react';
import {Box, ButtonContained, ButtonOutlined, Icon, List, RadioButton, Text, TextInput} from '@base-components';
import {CONTENT_CODES, isAndroid, isIos} from '@constants';
import {
  useEmailTrackingDeviceTypeConnection,
  useTrackingDevicePopUpState,
} from '@contexts';
import {useOnMount} from '@hooks';
import {
  allTrackingAppsAndroid,
  allTrackingDeviceTypesAndroid,
  allTrackingDeviceTypesIos,
  getTrackingAppDisplayName,
  getTrackingDeviceTypeDisplayName,
  isPrimaryTrackingDeviceType,
  type SetState,
  TrackingApps,
  TrackingDeviceTypes,
} from '@types';
import {memoComponent} from '@utils';
import {ManagedModal} from '../Shared';
import {FullHealthSyncReload} from './FullHealthSyncReload';
import {TrackingDeviceExplanations} from './TrackingDeviceExplanations';

const StartContent: React.FC = () => (
  <>
    <Text pb={2}>{CONTENT_CODES().EXPLANATIONS.TRACKING_DEVICE_POP_UP.START_DESCRIPTION_1}</Text>
    <Text>{CONTENT_CODES().EXPLANATIONS.TRACKING_DEVICE_POP_UP.START_DESCRIPTION_2}</Text>
  </>
);

const allDeviceTypes = isIos ? allTrackingDeviceTypesIos : allTrackingDeviceTypesAndroid;

const TrackingDeviceQuestion: React.FC<{
  localTrackingApp: string | undefined;
  localTrackingDeviceType: TrackingDeviceTypes | undefined;
  setTrackingDevice: (value: {
    trackingApp?: string;
    trackingDeviceType: TrackingDeviceTypes;
  }) => void;
}> = ({localTrackingApp, localTrackingDeviceType, setTrackingDevice}) => (
  <>
    <Text variant='labelMedium'>{CONTENT_CODES().EXPLANATIONS.TRACKING_DEVICE_POP_UP.ASH_WHICH_TRACKING_DEVICE}</Text>
    <List.Section>
      {allDeviceTypes.map(type => (
        <RadioButton
          key={`has-tracking-option-${type}`}
          isDense
          isFilled={type === localTrackingDeviceType}
          label={
            <Box alignItems='center' flexDirection='row'>
              <Text pl={1} variant='labelMedium'>{getTrackingDeviceTypeDisplayName(type)}</Text>
            </Box>
          }
          value={type}
          onPress={() => {
            setTrackingDevice({
              trackingDeviceType: type,
              ...(localTrackingApp && {trackingApp: localTrackingApp}),
            });
          }}
        />
      ))}
    </List.Section>
  </>
);

const AdditionalExplanation: React.FC<{
  localTrackingDeviceType: TrackingDeviceTypes | undefined;
}> = ({localTrackingDeviceType}) => {
  const emailWearableConnectionMutation = useEmailTrackingDeviceTypeConnection();

  useOnMount(() => {
    // Send tracking device email if supported
    if (isPrimaryTrackingDeviceType(localTrackingDeviceType)) {
      emailWearableConnectionMutation.mutate(localTrackingDeviceType);
    }
  });

  return <TrackingDeviceExplanations isContentOnly isShowEmail />;
};

const TrackingAppQuestion: React.FC<{
  localTrackingApp: string | undefined;
  localTrackingDeviceType: TrackingDeviceTypes | undefined;
  onContinue: () => void;
  setTrackingApp: (app: string) => void;
}> = ({localTrackingApp, localTrackingDeviceType, onContinue, setTrackingApp}) => {
  const [otherTrackingApp, setOtherTrackingApp] = useState('');

  // Continue if one of the tracking device options to prompt for
  const isStay =
    isAndroid &&
    localTrackingDeviceType &&
    (localTrackingDeviceType === TrackingDeviceTypes.NoDevice ||
      localTrackingDeviceType === TrackingDeviceTypes.Other);

  useOnMount(() => {
    if (!isStay) {
      onContinue();
    }
  });

  if (!isStay) return null;

  return (
    <>
      <Text>{CONTENT_CODES().EXPLANATIONS.TRACKING_DEVICE_POP_UP.ASK_WHICH_TRACKING_APP}</Text>
      <List.Section>
        {allTrackingAppsAndroid.map(type => (
          <RadioButton
            key={`has-tracking-app-option-${type}`}
            isDense
            isFilled={type === localTrackingApp}
            label={
              <Box alignItems='center' flexDirection='row'>
                <Text pl={1}>{getTrackingAppDisplayName(type)}</Text>
              </Box>
            }
            value={type}
            onPress={() => setTrackingApp(type)}
          />
        ))}
      </List.Section>
      {localTrackingApp === TrackingApps.Other && (
        <Box px={2}>
          <TextInput
            label={CONTENT_CODES().EXPLANATIONS.TRACKING_DEVICE_POP_UP.TRACKING_APP_LABEL}
            placeholder={
              CONTENT_CODES().EXPLANATIONS.TRACKING_DEVICE_POP_UP.TRACKING_APP_PLACEHOLDER
            }
            value={otherTrackingApp}
            onChangeText={setOtherTrackingApp}
          />
        </Box>
      )}
      <Box flexDirection='row' justifyContent='center' pt={2}>
        <ButtonContained
          onPress={() => {
            setTrackingApp(otherTrackingApp || localTrackingApp || '');
            onContinue();
          }}
        >
          {CONTENT_CODES().EXPLANATIONS.TRACKING_DEVICE_POP_UP.CONTINUE}
        </ButtonContained>
      </Box>
    </>
  );
};

const IntegrationSwitchConfirmation: React.FC<{
  localTrackingDeviceType: TrackingDeviceTypes | undefined;
  onCancel: () => void;
  onConfirm: () => void;
  serverTrackingDeviceType: TrackingDeviceTypes | undefined;
}> = ({localTrackingDeviceType, onCancel, onConfirm, serverTrackingDeviceType}) => (
  <>
    <Text pb={2}>
      You are about to switch from{' '}
      <Text variant='labelMedium'>
        {serverTrackingDeviceType ? getTrackingDeviceTypeDisplayName(serverTrackingDeviceType) : 'No Device'}
      </Text>{' '}
      to{' '}
      <Text variant='labelMedium'>
        {localTrackingDeviceType ? getTrackingDeviceTypeDisplayName(localTrackingDeviceType) : 'No Device'}
      </Text>
      .
    </Text>
    <Text pb={3}>
      {'Changing your health data source means you\'ll only sync data from the new device going forward. Don\'t worry - all your previously synced data will remain in the app. Do you want to continue with the switch?'}
    </Text>
    <Box flexDirection='column' justifyContent='center'>
      <ButtonContained onPress={onConfirm}>
        Yes, switch devices
      </ButtonContained>
      <ButtonOutlined mt={2} onPress={onCancel}>
        Cancel
      </ButtonOutlined>
    </Box>
  </>
);

const AllSetContent: React.FC<{isPending: boolean; setIsPending: (value: boolean) => void}> = ({
  isPending,
  setIsPending,
}) => (
  <>
    <Text pb={2} textAlign='center' variant='bodyLarge'>
      {isPending
        ? CONTENT_CODES().EXPLANATIONS.TRACKING_DEVICE_POP_UP.RELOADING_HEALTH_DATA
        : CONTENT_CODES().EXPLANATIONS.TRACKING_DEVICE_POP_UP.ALL_SET}
    </Text>
    {!isPending && (
      <Text>{CONTENT_CODES().EXPLANATIONS.TRACKING_DEVICE_POP_UP.ALL_SET_EXPLANATION}</Text>
    )}
    <FullHealthSyncReload enableOnMount hideUi onIsPendingChange={setIsPending} />
  </>
);

const ResetLoadingOnMount = ({setIsPending}: {setIsPending: SetState<boolean>}) => {
  useOnMount(() => {
    setIsPending(false);
  });

  return null;
};

type TrackingDeviceModalProps = Record<string, unknown>;

export const TrackingDeviceModal: React.FC<TrackingDeviceModalProps> = memoComponent(() => {
  const {
    isDismissable,
    localTrackingApp,
    localTrackingDeviceType,
    onCancelSwitch,
    onConfirmSwitch,
    onContinue,
    onDismiss,
    serverTrackingDeviceType,
    setTrackingApp,
    setTrackingDevice,
    trackingDevicePopUpState,
  } = useTrackingDevicePopUpState();
  const [isPending, setIsPending] = useState(false);
  const isDisabled = !isDismissable && trackingDevicePopUpState === 'closed';

  // Disable continue button if tracking device selection hasn't changed
  const hasSelectionChanged = localTrackingDeviceType !== serverTrackingDeviceType;
  const isContinueDisabled = trackingDevicePopUpState === 'deviceTrackingType' && !hasSelectionChanged;

  const continueButton = (
    <Box flexDirection='row' justifyContent='center' pt={2}>
      <ButtonContained
        disabled={isPending || isDisabled || isContinueDisabled}
        loading={isPending}
        onPress={onContinue}
      >
        {trackingDevicePopUpState === 'allSet'
          ? CONTENT_CODES().EXPLANATIONS.TRACKING_DEVICE_POP_UP.CLOSE
          : CONTENT_CODES().EXPLANATIONS.TRACKING_DEVICE_POP_UP.CONTINUE}
      </ButtonContained>
    </Box>
  );

  return (
    <ManagedModal
      isFullWidth
      isNewBackgroundColor
      isDismissable={isDismissable}
      isOpen={trackingDevicePopUpState !== 'closed'}
      onDismiss={onDismiss}
    >
      <ResetLoadingOnMount setIsPending={setIsPending} />
      <Box alignItems='center' pb={1}>
        <Icon name='watch' size={48} />
      </Box>

      <Text textAlign='center' variant='headlineMedium'>
        {CONTENT_CODES().EXPLANATIONS.TRACKING_DEVICE_POP_UP.HEADER}
      </Text>

      <Box py={1} />

      {trackingDevicePopUpState === 'start' && (
        <>
          <StartContent />
          {continueButton}
        </>
      )}
      {trackingDevicePopUpState === 'deviceTrackingType' && (
        <>
          <TrackingDeviceQuestion
            localTrackingApp={localTrackingApp}
            localTrackingDeviceType={localTrackingDeviceType}
            setTrackingDevice={setTrackingDevice}
          />
          {continueButton}
        </>
      )}
      {trackingDevicePopUpState === 'integrationSwitchConfirmation' && (
        <IntegrationSwitchConfirmation
          localTrackingDeviceType={localTrackingDeviceType}
          serverTrackingDeviceType={serverTrackingDeviceType}
          onCancel={onCancelSwitch}
          onConfirm={onConfirmSwitch}
        />
      )}
      {trackingDevicePopUpState === 'additionalExplanation' && (
        <>
          <AdditionalExplanation localTrackingDeviceType={localTrackingDeviceType} />
          {continueButton}
        </>
      )}
      {trackingDevicePopUpState === 'trackingApp' && (
        <TrackingAppQuestion
          localTrackingApp={localTrackingApp}
          localTrackingDeviceType={localTrackingDeviceType}
          setTrackingApp={setTrackingApp}
          onContinue={onContinue}
        />
      )}
      {trackingDevicePopUpState === 'allSet' && (
        <>
          <AllSetContent isPending={isPending} setIsPending={setIsPending} />
          {continueButton}
        </>
      )}
    </ManagedModal>
  );
});
