import {useMutation} from '@tanstack/react-query';
import {useAtomValue} from 'jotai';
import {useCallback, useMemo} from 'react';
import {firebaseApi} from '@api';
import {auth} from '@backend';
import {DEV_FEATURE_FLAGS, STORAGE_KEYS} from '@constants';
import {atomWithPersistentStorage, useDateEveryDayChanged, useDateEveryHourChanged} from '@hooks';
import {
  anyTimestampToFirestoreClientTimestamp,
  type AppUser,
  type AuthContextType,
  type Challenge,
  type ChallengeGroupParticipantDocument,
  ChallengeInviteStatus,
  dateToRawTimestamp,
  type FirebaseUser,
  type FitbitSettings,
  type Initializer,
  isAppUserAdmin,
  isAppUserTrainer,
  isGroupsChallenge,
  isParticipantsChallenge,
  type PartialUndefined,
  patternMatch,
  type PushNotificationSettings,
  StatusCodes,
  timestampToDate,
  TrackingDeviceTypes,
} from '@types';
import {
  authLogger,
  clearAllAuthStorage,
  clearStorageExceptStorageKeys,
  createMergeUpdatesAndRemoveUndefinedAndEmptyValuesCallback,
  createUseSetterFromAtom,
  emptyAuthContext,
  filterActiveChallenges,
  filterByUniqueProperty,
  filterDistinct,
  filterEditorChallenges,
  filterInactiveChallenges,
  fitbitLog,
  getAppUserHealthDataOnly,
  getHealthSyncStartDate,
  getInitializer,
  isDeepEqual,
  LOGGER,
  makeConditionalRenderWrapper,
  mergeUpdatesAndRemoveUndefinedValues,
  notificationLog,
  reloadApp,
  sortChallengesDescendingByStartDate,
} from '@utils';
import {
  useAppConfig,
  useAppUserGroupsChallengesAll,
  useAppUserPartialMutation,
  useAppUserParticipantChallengesAll,
  useGetIsValidNewUserCredentials,
} from './firestore';
import {useAddSnack} from './snacks';

/** App User & Auth User Local Context state */
const authContextAtom = atomWithPersistentStorage(STORAGE_KEYS.AUTH_CONTEXT, emptyAuthContext);

const mergePropertiesCallback =
  createMergeUpdatesAndRemoveUndefinedAndEmptyValuesCallback<AuthContextType>(
    mergeUpdatesAndRemoveUndefinedValues,
  );

const useSetAuthContext = createUseSetterFromAtom(authContextAtom, mergePropertiesCallback);

export const useUpdateAppUser = () =>
  useSetAuthContext(
    (prev, appUser: Initializer<AppUser | undefined>) => ({
      ...prev,
      appUser: getInitializer(appUser, prev.appUser),
    }),
    {
      isDisabled: DEV_FEATURE_FLAGS().isMockUserEnabled,
      debug: DEV_FEATURE_FLAGS().isDebugAppUserChanged,
    },
  );

export const useUpdateFirebaseUser = () =>
  useSetAuthContext(
    (prev, firebaseUser: Initializer<Partial<FirebaseUser> | undefined>) => ({
      ...prev,
      firebaseUser: getInitializer(firebaseUser, prev.firebaseUser),
    }),
    {
      isDisabled: DEV_FEATURE_FLAGS().isMockUserEnabled,
    },
  );

export const useUpdateIsPerformingInitialLoad = () =>
  useSetAuthContext((prev, isPerformingInitialLoad: Initializer<boolean>) => ({
    ...prev,
    isPerformingInitialLoad: getInitializer(isPerformingInitialLoad, prev.isPerformingInitialLoad),
  }));

/** Auth related READ hooks */

export const useAppUser = () => useAtomValue(authContextAtom).appUser;
export const useAppUserSafe = () => {
  const {appUser} = useAtomValue(authContextAtom);
  if (!appUser) throw new Error('App user is undefined when accessing user');

  return appUser;
};

const useOptimisticAppUserUpdater = () => {
  const appUser = useAppUserSafe(); // Gets the current user from your cache
  const setAppUser = useUpdateAppUser(); // Updates the cached app user
  const {mutateAsync} = useAppUserPartialMutation(appUser.id); // Firestore mutation hook

  return useCallback(
    async (updates: Partial<AppUser>) => {
      // Optimistically update the local cache
      setAppUser({...appUser, ...updates});

      try {
        // Persist to Firestore
        await mutateAsync(updates);
      } catch (error) {
        LOGGER.error('Failed to update Firestore:', error);

        // Rollback to the previous state if Firestore update fails
        setAppUser(appUser);
      }
    },
    [appUser, setAppUser, mutateAsync],
  );
};

export const useAppUserHealthDataOnly = () => {
  const appUser = useAppUserSafe();
  return useMemo(() => getAppUserHealthDataOnly(appUser), [appUser]);
};

export const useFirebaseUser = () => useAtomValue(authContextAtom).firebaseUser;
export const useIsPerformingInitialLoad = () =>
  useAtomValue(authContextAtom).isPerformingInitialLoad;
export const useIsFirebaseAuthReady = () => !useIsPerformingInitialLoad();

export const useIsAuthenticated = () => {
  const hasFirebaseUser = !!useFirebaseUser();
  const hasAppUser = !!useAppUser();
  return hasFirebaseUser && hasAppUser;
};

export const useHasAcceptedPrivacyPolicy = () => {
  const {hasAcceptedPrivacyPolicy = false} = useAppUser() ?? {};
  return hasAcceptedPrivacyPolicy;
};

export const useIsPrivacyPolicyPending = () => !useHasAcceptedPrivacyPolicy();

/**
 * Returns true if the user is a coach, trainer, or admin
 */
export const useIsCoach = () => {
  const appUser = useAppUser();
  return appUser ? isAppUserTrainer(appUser) : false;
};

/**
 * Returns true if the user is an admin
 */
export const useIsAdmin = () => {
  const appUser = useAppUser();
  return appUser ? isAppUserAdmin(appUser) : false;
};

export const IsCoachWrapper = makeConditionalRenderWrapper(useIsCoach);

export const useIsValidNewUserCredential = (
  credential: string | undefined,
  initialCredential: string | undefined,
  isPhone?: true,
) => {
  const {data, isPending: isLoading} = useGetIsValidNewUserCredentials(
    isPhone ? {phoneNumber: credential} : {email: credential},
  );
  const isValid = (!!initialCredential && initialCredential === credential) || data?.isValid;
  const errorMessage = data?.errorMessage;
  return {isValid, errorMessage, isLoading};
};

export const useTrackingDeviceType = () => {
  const appUser = useAppUserSafe();
  return appUser.trackingDeviceType ?? appUser.wearableDeviceBrand;
};

export const useTrackingApp = () => useAppUserSafe().trackingApp;

export const useGetHealthSyncStartDate = (overrideDate?: Date): Date => {
  const appUser = useAppUserSafe();
  const appConfig = useAppConfig();
  const currentDate = useDateEveryHourChanged();
  return useMemo(() => {
    if (overrideDate) return overrideDate;
    // TEMPORARY: For Fitbit to fix override issue - but respect integration switches
    if (appUser.fitbit && !appUser.lastHealthIntegrationSwitch) {
      return timestampToDate(appUser.accountCreatedDateTime);
    }

    return getHealthSyncStartDate(
      appUser.accountCreatedDateTime,
      appUser.lastHealthStatsSync,
      appConfig.healthSyncMinMsAgo,
      currentDate,
      appUser.lastHealthIntegrationSwitch?.switchDate,
    );
  }, [
    appConfig.healthSyncMinMsAgo,
    appUser.accountCreatedDateTime,
    appUser.fitbit,
    appUser.lastHealthStatsSync,
    appUser.lastHealthIntegrationSwitch,
    currentDate,
    overrideDate,
  ]);
};

export const usePushNotificationSettings = () => useAppUserSafe().pushNotificationSettings;

/** Auth Related HELPER functions */

export const withLastModifiedTime = (appUser: PartialUndefined<AppUser>, date = new Date()) => ({
  ...appUser,
  lastModifiedDateTime: anyTimestampToFirestoreClientTimestamp(date),
});

const withLastHealthStatsSync = (appUser: PartialUndefined<AppUser>, date = new Date()) => ({
  ...appUser,
  lastHealthStatsSync: anyTimestampToFirestoreClientTimestamp(date),
});

export const useLogoutMutation = () => {
  const setFirebaseUser = useUpdateFirebaseUser();
  const setAppUser = useUpdateAppUser();
  const addSnack = useAddSnack();

  const mutationFn = useCallback(async () => {
    try {
      authLogger('logging out user...');

      const response = await firebaseApi.logoutAuthUser();
      if (response.status !== StatusCodes.OK_200) {
        LOGGER.error('Error logging out', response.data);
        addSnack('There was an issue logging out');
        return;
      }
      await auth.signOut();
      // Clear auth storage after async sign out
      clearAllAuthStorage();
      clearStorageExceptStorageKeys();
      setAppUser(undefined);
      setFirebaseUser(undefined);

      addSnack('Logged out successfully ✅');
    } catch (error) {
      LOGGER.error('Error logging out', error);
      addSnack('There was an issue logging out');
      await reloadApp();
    }
  }, [addSnack, setAppUser, setFirebaseUser]);

  return useMutation({mutationFn, mutationKey: ['useLogOutAppUser']});
};

/** CHALLENGE related hooks */

export const useIsChallengePending = (
  challenge: Challenge,
  challengeGroupParticipant: ChallengeGroupParticipantDocument | undefined,
) => {
  const {id} = useAppUserSafe();

  return isParticipantsChallenge(challenge)
    ? !!challenge.participants.some(
        p => p.id === id && p.inviteStatus === ChallengeInviteStatus.PENDING,
      )
    : challengeGroupParticipant?.inviteStatus === ChallengeInviteStatus.PENDING;
};

export const useChallengesForUser = () => {
  const {id: userId} = useAppUserSafe();
  const isTrainer = useIsCoach();
  const participantChallenges = useAppUserParticipantChallengesAll(userId);
  const {challengeGroupParticipants, groupsChallenges} = useAppUserGroupsChallengesAll(userId);
  const allChallenges = useMemo(
    () =>
      [...participantChallenges, ...(groupsChallenges ?? [])]
        .filter(filterByUniqueProperty('id'))
        .filter(c => isTrainer || !c.flags?.isDraft),
    [participantChallenges, groupsChallenges, isTrainer],
  );
  const today = useDateEveryDayChanged();

  return useMemo(() => {
    const filterByInviteStatus = (status: ChallengeInviteStatus) => (c: Challenge) =>
      patternMatch(c)
        .when(isParticipantsChallenge, value =>
          value.participants.some(p => p.id === userId && p.inviteStatus === status),
        )
        .when(isGroupsChallenge, value =>
          challengeGroupParticipants?.find(
            p => p.challengeId === value.id && p.id === userId && p.inviteStatus === status,
          ),
        )
        .exhaustive();

    const isParticipantInChallenge = (c: Challenge) =>
      patternMatch(c)
        .when(isParticipantsChallenge, value => value.participants.some(p => p.id === userId))
        .when(isGroupsChallenge, value =>
          challengeGroupParticipants?.find(p => p.challengeId === value.id && p.id === userId),
        )
        .exhaustive();

    // Build challenge arrays incrementally
    const activeChallenges = allChallenges
      .filter(filterActiveChallenges(today))
      .sort(sortChallengesDescendingByStartDate);

    const inactiveChallenges = allChallenges
      .filter(filterInactiveChallenges(today))
      .sort(sortChallengesDescendingByStartDate);

    const activeEditorChallenges = activeChallenges.filter(filterEditorChallenges(userId));
    const activeParticipantChallenges = activeChallenges.filter(isParticipantInChallenge);

    const acceptedParticipantChallenges = activeParticipantChallenges.filter(
      filterByInviteStatus(ChallengeInviteStatus.ACCEPTED),
    );

    const pendingChallenges = activeParticipantChallenges.filter(
      filterByInviteStatus(ChallengeInviteStatus.PENDING),
    );

    const acceptedParticipantOrEditorChallenges = [
      ...acceptedParticipantChallenges,
      ...activeEditorChallenges,
    ].filter(filterDistinct);

    return {
      activeChallenges,
      inactiveChallenges,
      pendingChallenges,
      activeEditorChallenges,
      acceptedParticipantOrEditorChallenges,
    };
  }, [allChallenges, today, userId, challengeGroupParticipants]);
};

export const useChallengeForceRefresh = () => {
  const appUser = useAppUserSafe();
  const {mutateAsync} = useAppUserPartialMutation(appUser.id);
  return useMutation({
    mutationFn: async () => {
      const now = new Date();
      const partialUpdates = withLastHealthStatsSync(withLastModifiedTime({}, now), now);
      // Force update a user
      await mutateAsync(partialUpdates, {
        onSuccess: () => {
          authLogger('[Challenge] user forced update last login timestamp');
        },
      });
    },
  });
};

/** EDIT USER related hooks */

export const useAppUserUpdatePrivacyPolicy = () => {
  const appUser = useAppUserSafe();
  const {isPending: isPendingAccept, mutateAsync: accept} = useAppUserPartialMutation(appUser.id);
  const {isPending: isPendingLogout, mutateAsync: logout} = useLogoutMutation();

  const onAccept = useCallback(async () => {
    authLogger('[PrivacyPolicy] privacy policy accepted');
    await accept({hasAcceptedPrivacyPolicy: true});
  }, [accept]);

  const onDismiss = useCallback(() => {
    authLogger('[PrivacyPolicy] privacy policy dismissed');
    return logout();
  }, [logout]);

  const {hasAcceptedPrivacyPolicy} = appUser;

  return {
    onAccept,
    onDismiss,
    hasAcceptedPrivacyPolicy,
    isPendingAccept,
    isPendingLogout,
  };
};

export const useUpdateAppUserFitbitSettings = () => {
  const appUser = useAppUserSafe();
  const {mutateAsync} = useAppUserPartialMutation(appUser.id);
  return useMutation({
    mutationFn: async (fitbitSettings: FitbitSettings | undefined) => {
      fitbitLog('Updating fitbit settings...');
      await mutateAsync({fitbit: fitbitSettings!});
    },
    mutationKey: ['useUpdateAppUserFitbitSettings', appUser.id],
  });
};

export const DEFAULT_PUSH_NOTIFICATION_SETTINGS: PushNotificationSettings = {
  isNotificationEnabled: true,
};

export const useUpdatePushNotificationSettings = () => {
  const appUser = useAppUserSafe();
  const updateAppUser = useOptimisticAppUserUpdater();
  const mutationFn = useCallback(
    async (settingsInitializer: Initializer<PushNotificationSettings | undefined>) => {
      const pushNotificationSettings =
        getInitializer(
          settingsInitializer,
          appUser.pushNotificationSettings ?? DEFAULT_PUSH_NOTIFICATION_SETTINGS,
        ) ?? DEFAULT_PUSH_NOTIFICATION_SETTINGS;
      // Skip if value unchanged
      const hasChanged = !isDeepEqual(pushNotificationSettings, appUser.pushNotificationSettings);
      if (!hasChanged) return;
      notificationLog('Updating push notification settings...', pushNotificationSettings);
      await updateAppUser({pushNotificationSettings});
    },
    [updateAppUser, appUser.pushNotificationSettings],
  );
  return useMutation({
    mutationFn,
    mutationKey: ['useUpdatePushNotificationSettings', appUser.id],
  });
};

export const useSetTrackingDeviceType = () => {
  const appUser = useAppUserSafe();
  const {mutateAsync} = useAppUserPartialMutation(appUser.id);
  return useCallback(
    async (value: {
      trackingApp?: string | undefined;
      trackingDeviceType?: TrackingDeviceTypes | undefined;
    }) => {
      authLogger('Updating tracking device type, tracking app, and isMileageGpsSourced...');

      // Capture current values before mutation to detect switches
      const currentDeviceType = appUser.trackingDeviceType;
      const currentApp = appUser.trackingApp;

      // Determine if this is an actual switch (not initial setup)
      const isInitialSetup = currentDeviceType === undefined;
      const hasDeviceTypeChanged = value.trackingDeviceType !== currentDeviceType;
      const hasAppChanged = value.trackingApp !== currentApp;
      const isActualSwitch = !isInitialSetup && (hasDeviceTypeChanged || hasAppChanged);

      await mutateAsync({
        wearableDeviceBrand: value.trackingDeviceType!,
        trackingDeviceType: value.trackingDeviceType!,
        trackingApp:
          value.trackingDeviceType === TrackingDeviceTypes.NoDevice ||
          value.trackingDeviceType === TrackingDeviceTypes.Other
            ? value.trackingApp
            : undefined,
        ...(value.trackingDeviceType !== undefined &&
        // Phones don't accurately track distance
          value.trackingDeviceType !== TrackingDeviceTypes.Phone &&
        // Samsung Galaxy devices never track distances
          value.trackingDeviceType !== TrackingDeviceTypes.Samsung
          ? {isMileageGpsSourced: true}
          : {isMileageGpsSourced: undefined}),
        // Record integration switch if this is an actual switch
        ...(isActualSwitch && {
          lastHealthIntegrationSwitch: {
            switchDate: dateToRawTimestamp(new Date()),
            previousDeviceType: currentDeviceType,
            previousApp: currentApp,
          },
        }),
      });
    },
    [mutateAsync, appUser.trackingDeviceType, appUser.trackingApp],
  );
};

export const useSetTrackingApp = () => {
  const appUser = useAppUserSafe();
  const {mutateAsync} = useAppUserPartialMutation(appUser.id);
  return useCallback(
    async (trackingApp: string) => {
      authLogger('Updating tracking app');
      await mutateAsync({trackingApp});
    },
    [mutateAsync],
  );
};
