import {useState} from 'react';
import {Box, ButtonOutlined, LoaderWrapper, Text} from '@base-components';
import {
  EditHealthProfileInfoModal,
  FullHealthSyncReload,
  HealthSyncStepModal,
  HealthSyncSystemIcon,
  HealthSyncWeightModal,
  ScreenContent,
  ScreenHeader,
  TrackingDeviceExplanations,
  TrackingDeviceModal,
  TrackingDeviceReset,
  TrackingDeviceTypeLabel,
} from '@components';
import {CONTENT_CODES, isIos} from '@constants';
import {
  ScreenWrapper,
  useAppUserSafe,
  useFitbitConnected,
  useHasAllHealthPermissions,
  useHasAnyHealthPermissions,
} from '@contexts';
import {onLinkToHealthApp} from '@navigation';

type HealthSyncSettingsScreenProps = Record<string, unknown>;
export const HealthSyncSettingsScreen: React.FC<HealthSyncSettingsScreenProps> = () => {
  const hasAnyHealthPermissions = useHasAnyHealthPermissions();
  const isFitbitConnected = useFitbitConnected();
  const {data: hasAllPermissions} = useHasAllHealthPermissions();
  const [isEditProfileOpen, setIsEditProfileOpen] = useState(false);
  const appUser = useAppUserSafe();

  return (
    <ScreenWrapper>
      <ScreenHeader title={CONTENT_CODES().SETTINGS.HEALTH_SYNC.HEADER} />

      <ScreenContent>
        <Box pt={1} />

        <LoaderWrapper isLoading={isFitbitConnected === undefined}>
          {!isFitbitConnected && (
            <>
              <Box alignItems='center' flexDirection='row'>
                <HealthSyncSystemIcon />
                <Box pr={2} />
                <Text variant='bodyLarge'>
                  {hasAllPermissions && CONTENT_CODES().SETTINGS.HEALTH_SYNC.HAS_ALL_PERMISSIONS}
                  {!hasAllPermissions &&
                    hasAnyHealthPermissions &&
                    CONTENT_CODES().SETTINGS.HEALTH_SYNC.HAS_PARTIAL_PERMISSIONS}
                  {!hasAnyHealthPermissions &&
                    CONTENT_CODES().SETTINGS.HEALTH_SYNC.HAS_NO_PERMISSIONS}
                </Text>
              </Box>

              {!hasAllPermissions && <Box pt={2} />}
              <HealthSyncStepModal />
              <HealthSyncWeightModal />

              {/* {isAndroid && <HealthConnectDataSourceSettings />} */}

              <Box pt={2} />
            </>
          )}
        </LoaderWrapper>

        <TrackingDeviceTypeLabel />

        <TrackingDeviceExplanations />

        <ButtonOutlined icon='pencil' mt={2} onPress={() => setIsEditProfileOpen(true)}>
          {CONTENT_CODES().SETTINGS.HEALTH_SYNC.EDIT_PROFILE_BUTTON}
        </ButtonOutlined>
        <EditHealthProfileInfoModal
          initialState={appUser}
          isOpen={isEditProfileOpen}
          onDismiss={() => setIsEditProfileOpen(false)}
        />

        {!isFitbitConnected && (
          <ButtonOutlined
            icon={isIos ? 'apple' : 'android'}
            mt={2}
            onPress={onLinkToHealthApp}
          >
            {CONTENT_CODES().SETTINGS.APP_SETTINGS.HEALTH_APP_LINK}
          </ButtonOutlined>
        )}
        {(!!hasAnyHealthPermissions || isFitbitConnected) && <FullHealthSyncReload />}

        <TrackingDeviceReset />
        <TrackingDeviceModal />
      </ScreenContent>
    </ScreenWrapper>
  );
};
